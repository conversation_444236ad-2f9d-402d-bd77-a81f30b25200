<!doctype html>
<html lang="zh" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-aiflow-tricks-and-tips/Group-Nodes" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">Leveraging Group Nodes | FunBlocks AI</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Group-Nodes"><meta data-rh="true" property="og:locale" content="zh"><meta data-rh="true" property="og:locale:alternate" content="en"><meta data-rh="true" name="docusaurus_locale" content="zh"><meta data-rh="true" name="docsearch:language" content="zh"><meta data-rh="true" name="keywords" content="FunBlocks AI, AI Tools, AI Mindmap generator, infographic generator, brainstorming, AI ideation, AI writing, AI reading, AI image generate, FunBlocks AIFlow, Prompt Optimizer, AI Prompt, ChatGPT Prompt, Claude Prompt, Gemini Prompt"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Leveraging Group Nodes | FunBlocks AI"><meta data-rh="true" name="description" content="Group nodes are a powerful feature in FunBlocks AIFlow that can transform how you organize and process your ideas. Let&#x27;s explore how to use them effectively and what benefits they bring to your workflow."><meta data-rh="true" property="og:description" content="Group nodes are a powerful feature in FunBlocks AIFlow that can transform how you organize and process your ideas. Let&#x27;s explore how to use them effectively and what benefits they bring to your workflow."><link data-rh="true" rel="icon" href="/zh/img/icon.png"><link data-rh="true" rel="canonical" href="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Group-Nodes"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Group-Nodes" hreflang="en"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/zh/docs/aiflow-tricks-and-tips/Group-Nodes" hreflang="zh"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/docs/aiflow-tricks-and-tips/Group-Nodes" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/zh/blog/rss.xml" title="FunBlocks AI RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/zh/blog/atom.xml" title="FunBlocks AI Atom Feed"><link rel="stylesheet" href="/zh/assets/css/styles.fec1ab2f.css">
<script src="/zh/assets/js/runtime~main.98b31ef5.js" defer="defer"></script>
<script src="/zh/assets/js/main.6ede2ef8.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/zh/img/icon.png"><div role="region" aria-label="跳到主要内容"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">跳到主要内容</a></div><nav aria-label="主导航" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="切换导航栏" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/zh/"><div class="navbar__logo"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">FunBlocks</b></a><a class="navbar__item navbar__link" href="/zh/aiflow">AIFlow</a><a href="https://www.funblocks.net/aitools" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">AI Tools</a><a class="navbar__item navbar__link" href="/zh/slides">AI Slides</a><a class="navbar__item navbar__link" href="/zh/aidocs">AI Docs</a><a class="navbar__item navbar__link" href="/zh/welcome_extension">AI Extension</a><a class="navbar__item navbar__link" href="/zh/prompt-optimizer">Prompt Optimizer</a><a href="https://app.funblocks.net/#/aiplans" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Pricing</a></div><div class="navbar__items navbar__items--right"><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/zh/docs/funblocks">Tutorial</a><a class="navbar__item navbar__link" href="/zh/thinking-matters/behind-aiflow">Thinking Matters</a><a class="navbar__item navbar__link" href="/zh/blog">Blog</a><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_nlXk"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>中文</a><ul class="dropdown__menu"><li><a href="/docs/aiflow-tricks-and-tips/Group-Nodes" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="en">English</a></li><li><a href="/zh/docs/aiflow-tricks-and-tips/Group-Nodes" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="zh">中文</a></li></ul></div><div class="navbarSearchContainer_Bca1"></div><div><div class="btn_Tj_u btnSm_Ghhp" href="https://app.funblocks.net/#/login?source=flow">Login</div></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="回到顶部" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="文档侧边栏" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/zh/docs/funblocks">FunBlocks AI</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/zh/docs/category/funblocks-product-suite">FunBlocks Product Suite</a><button aria-label="展开侧边栏分类 &#x27;FunBlocks Product Suite&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" href="/zh/docs/category/aiflow-tricks-and-tips">AIFlow Tricks and Tips</a><button aria-label="折叠侧边栏分类 &#x27;AIFlow Tricks and Tips&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Boundless-Canvas-Mindmap">Infinite Canvas</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Asking-Good-Questions">Communicate Effectively with AI</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Mindmap-Generator">Mind Map Generator</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Brainstorming">Brainstorming and Ideation</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Breakdown">Breakdown</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Critical-Thinking">Enhancing Critical Thinking Skills</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/AI-Tools">Unleash AIFlow: AI Tools Tailored for Your Needs</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/From-Ideas-to-Action">From Ideas to Action</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Reflection">Optimizing AI Output</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Notes">Maximizing Sticky Notes</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Image-Node">Mastering the Image Node</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Group-Nodes">Leveraging Group Nodes</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Infographics-generator">Infographics Generator</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Prompts">Creating Custom AI Applications</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/zh/docs/aiflow-tricks-and-tips/Support-Top-Tier-LLM">Multi-LLM Support</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/zh/docs/category/ai-tools">AI Tools</a><button aria-label="展开侧边栏分类 &#x27;AI Tools&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="页面路径"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="主页面" class="breadcrumbs__link" href="/zh/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/zh/docs/category/aiflow-tricks-and-tips"><span itemprop="name">AIFlow Tricks and Tips</span></a><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">Leveraging Group Nodes</span><meta itemprop="position" content="2"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">本页总览</button></div><div class="theme-doc-markdown markdown"><header><h1>Leveraging Group Nodes</h1></header>
<p>Group nodes are a powerful feature in FunBlocks AIFlow that can transform how you organize and process your ideas. Let&#x27;s explore how to use them effectively and what benefits they bring to your workflow.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="what-happens-when-you-group-multiple-nodes">What Happens When You Group Multiple Nodes?<a href="#what-happens-when-you-group-multiple-nodes" class="hash-link" aria-label="What Happens When You Group Multiple Nodes?的直接链接" title="What Happens When You Group Multiple Nodes?的直接链接">​</a></h2>
<p>When you group nodes in FunBlocks AIFlow, you unlock several advantages:</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="organization-and-structure">Organization and Structure<a href="#organization-and-structure" class="hash-link" aria-label="Organization and Structure的直接链接" title="Organization and Structure的直接链接">​</a></h3>
<p>First and foremost, grouping serves as an organizational tool. It transforms scattered nodes on your whiteboard into structured clusters based on your chosen principles or categories. This visual organization makes your workspace more navigable and your thinking more clear.</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="unified-management">Unified Management<a href="#unified-management" class="hash-link" aria-label="Unified Management的直接链接" title="Unified Management的直接链接">​</a></h3>
<p>Grouped nodes can be treated as a single entity, allowing you to move, resize, or perform other operations on the entire collection at once. This saves time and maintains the relationships between connected ideas.</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="enhanced-ai-capabilities">Enhanced AI Capabilities<a href="#enhanced-ai-capabilities" class="hash-link" aria-label="Enhanced AI Capabilities的直接链接" title="Enhanced AI Capabilities的直接链接">​</a></h3>
<p>Like individual nodes, group nodes have access to AI assistant menus. The key difference? The AI can use all content from child nodes within the group as context. This enables powerful applications such as generating:</p>
<ul>
<li>Comprehensive summaries</li>
<li>Professional slideshows</li>
<li>Detailed reports</li>
<li>Structured documents</li>
</ul>
<p><img decoding="async" loading="lazy" alt="FunBlocks AIFlow transform mindmap to slides with one-click" src="/zh/assets/images/aiflow_slides_generation-b5f781ef1c3d2676c0f621d694409959.png" width="2568" height="1494" class="img_ev3q"></p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="practical-applications-of-group-nodes">Practical Applications of Group Nodes<a href="#practical-applications-of-group-nodes" class="hash-link" aria-label="Practical Applications of Group Nodes的直接链接" title="Practical Applications of Group Nodes的直接链接">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="brainstorming-sessions">Brainstorming Sessions<a href="#brainstorming-sessions" class="hash-link" aria-label="Brainstorming Sessions的直接链接" title="Brainstorming Sessions的直接链接">​</a></h3>
<p>During brainstorming, you often generate numerous ideas across different categories. With group nodes, you can:</p>
<ol>
<li>Categorize related ideas into separate groups</li>
<li>Process each group individually with AI assistance</li>
<li>Generate tailored summaries or presentations for each category</li>
<li>Create comprehensive reports that maintain the logical structure of your thinking</li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="ongoing-idea-management">Ongoing Idea Management<a href="#ongoing-idea-management" class="hash-link" aria-label="Ongoing Idea Management的直接链接" title="Ongoing Idea Management的直接链接">​</a></h3>
<p>For those who continuously capture thoughts and insights, group nodes offer a flexible organization system:</p>
<ol>
<li>Classify random ideas into thematic groups as they emerge</li>
<li>Revisit any group when you&#x27;re ready to develop those concepts further</li>
<li>Work with the AI assistant to explore specific idea clusters in depth</li>
<li>Transform loosely connected thoughts into cohesive projects</li>
</ol>
<p><img decoding="async" loading="lazy" alt="FunBlocks AIFlow group nodes" src="/zh/assets/images/aiflow_group_nodes-b5e3be6b75f0ce312570b4b9c0b9bfe4.png" width="2082" height="1046" class="img_ev3q"></p>
<p>By mastering group nodes in FunBlocks AIFlow, you&#x27;ll bring a new level of organization and AI-powered processing to your creative and analytical workflows.</p></div></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="文件选项卡"><a class="pagination-nav__link pagination-nav__link--prev" href="/zh/docs/aiflow-tricks-and-tips/Image-Node"><div class="pagination-nav__sublabel">上一页</div><div class="pagination-nav__label">Mastering the Image Node</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/zh/docs/aiflow-tricks-and-tips/Infographics-generator"><div class="pagination-nav__sublabel">下一页</div><div class="pagination-nav__label">Infographics Generator</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#what-happens-when-you-group-multiple-nodes" class="table-of-contents__link toc-highlight">What Happens When You Group Multiple Nodes?</a><ul><li><a href="#organization-and-structure" class="table-of-contents__link toc-highlight">Organization and Structure</a></li><li><a href="#unified-management" class="table-of-contents__link toc-highlight">Unified Management</a></li><li><a href="#enhanced-ai-capabilities" class="table-of-contents__link toc-highlight">Enhanced AI Capabilities</a></li></ul></li><li><a href="#practical-applications-of-group-nodes" class="table-of-contents__link toc-highlight">Practical Applications of Group Nodes</a><ul><li><a href="#brainstorming-sessions" class="table-of-contents__link toc-highlight">Brainstorming Sessions</a></li><li><a href="#ongoing-idea-management" class="table-of-contents__link toc-highlight">Ongoing Idea Management</a></li></ul></li></ul></div></div></div></div></main></div></div></div></div>
</body>
</html>