<!doctype html>
<html lang="zh" dir="ltr" class="blog-wrapper blog-list-page plugin-blog plugin-id-default" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">FunBlocks AI Blog: Insights and Innovations | FunBlocks AI</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://www.funblocks.net/zh/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://www.funblocks.net/zh/blog/page/3"><meta data-rh="true" property="og:locale" content="zh"><meta data-rh="true" property="og:locale:alternate" content="en"><meta data-rh="true" name="docusaurus_locale" content="zh"><meta data-rh="true" name="docsearch:language" content="zh"><meta data-rh="true" name="keywords" content="FunBlocks AI, AI Tools, AI Mindmap generator, infographic generator, brainstorming, AI ideation, AI writing, AI reading, AI image generate, FunBlocks AIFlow, Prompt Optimizer, AI Prompt, ChatGPT Prompt, Claude Prompt, Gemini Prompt"><meta data-rh="true" property="og:title" content="FunBlocks AI Blog: Insights and Innovations | FunBlocks AI"><meta data-rh="true" name="description" content="Explore tips, tricks, and insights to maximize your FunBlocks AI experience!"><meta data-rh="true" property="og:description" content="Explore tips, tricks, and insights to maximize your FunBlocks AI experience!"><meta data-rh="true" name="docusaurus_tag" content="blog_posts_list"><meta data-rh="true" name="docsearch:docusaurus_tag" content="blog_posts_list"><link data-rh="true" rel="icon" href="/zh/img/icon.png"><link data-rh="true" rel="canonical" href="https://www.funblocks.net/zh/blog/page/3"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/blog/page/3" hreflang="en"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/zh/blog/page/3" hreflang="zh"><link data-rh="true" rel="alternate" href="https://www.funblocks.net/blog/page/3" hreflang="x-default"><script data-rh="true" type="application/ld+json">{"@context":"https://schema.org","@type":"Blog","@id":"https://www.funblocks.net/zh/blog/page/3","mainEntityOfPage":"https://www.funblocks.net/zh/blog/page/3","headline":"FunBlocks AI Blog: Insights and Innovations","description":"Explore tips, tricks, and insights to maximize your FunBlocks AI experience!","blogPost":[{"@type":"BlogPosting","@id":"https://www.funblocks.net/zh/blog/when-the-internet-meets-ghibli-how-aiflow-turns-your-photos-into-an-animated-world","mainEntityOfPage":"https://www.funblocks.net/zh/blog/when-the-internet-meets-ghibli-how-aiflow-turns-your-photos-into-an-animated-world","url":"https://www.funblocks.net/zh/blog/when-the-internet-meets-ghibli-how-aiflow-turns-your-photos-into-an-animated-world","headline":"When the Internet Meets Ghibli - How AIFlow Turns Your Photos into an Animated World","name":"When the Internet Meets Ghibli - How AIFlow Turns Your Photos into an Animated World","description":"Social media has recently been swept away by a wonderful style: your friends, the accounts you follow, and even some unexpected brands, their avatars and shared photos all seem to have taken on a dreamlike color, full of the gentleness and whimsy of “Spirited Away” and “My Neighbor Totoro.” This trend, known as “Ghiblification,” is spreading across the internet at an astonishing rate.","datePublished":"2025-03-29T00:00:00.000Z","author":{"@type":"Person","name":"Wood Peng","description":"SDE (Someone Do Everything) @ FunBlocks","url":"https://x.com/@woodpeng"},"keywords":[]}]}</script><link rel="alternate" type="application/rss+xml" href="/zh/blog/rss.xml" title="FunBlocks AI RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/zh/blog/atom.xml" title="FunBlocks AI Atom Feed"><link rel="stylesheet" href="/zh/assets/css/styles.fec1ab2f.css">
<script src="/zh/assets/js/runtime~main.98b31ef5.js" defer="defer"></script>
<script src="/zh/assets/js/main.6ede2ef8.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/zh/img/icon.png"><div role="region" aria-label="跳到主要内容"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">跳到主要内容</a></div><nav aria-label="主导航" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="切换导航栏" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/zh/"><div class="navbar__logo"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/zh/img/icon.png" alt="FunBlocks Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">FunBlocks</b></a><a class="navbar__item navbar__link" href="/zh/aiflow">AIFlow</a><a href="https://www.funblocks.net/aitools" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">AI Tools</a><a class="navbar__item navbar__link" href="/zh/slides">AI Slides</a><a class="navbar__item navbar__link" href="/zh/aidocs">AI Docs</a><a class="navbar__item navbar__link" href="/zh/welcome_extension">AI Extension</a><a class="navbar__item navbar__link" href="/zh/prompt-optimizer">Prompt Optimizer</a><a href="https://app.funblocks.net/#/aiplans" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Pricing</a></div><div class="navbar__items navbar__items--right"><a class="navbar__item navbar__link" href="/zh/docs/funblocks">Tutorial</a><a class="navbar__item navbar__link" href="/zh/thinking-matters/behind-aiflow">Thinking Matters</a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/zh/blog">Blog</a><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_nlXk"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>中文</a><ul class="dropdown__menu"><li><a href="/blog/page/3" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="en">English</a></li><li><a href="/zh/blog/page/3" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="zh">中文</a></li></ul></div><div class="navbarSearchContainer_Bca1"></div><div><div class="btn_Tj_u btnSm_Ghhp" href="https://app.funblocks.net/#/login?source=flow">Login</div></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="container margin-vert--lg"><div class="row"><aside class="col col--3"><nav class="sidebar_re4s thin-scrollbar" aria-label="最近博文导航"><div class="sidebarItemTitle_pO2u margin-bottom--md">Recent posts</div><div role="group"><h3 class="yearGroupHeading_rMGB">2025</h3><ul class="sidebarItemList_Yudw clean-list"><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/2025/06/15/The-Intersection-of-Epistemology-and-Artificial-Intelligence">The Intersection of Epistemology and Artificial Intelligence - Conceptual Distinctions, Theoretical Challenges, and Future Landscapes</a></li><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/2025/06/10/FunBlocks-AI-Empowers-Education-Ushering-in-a-New-Era-of-Smart-Learning">FunBlocks AI Empowers Education - Ushering in a New Era of Smart Learning</a></li><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/when-the-internet-meets-ghibli-how-aiflow-turns-your-photos-into-an-animated-world">When the Internet Meets Ghibli - How AIFlow Turns Your Photos into an Animated World</a></li><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/Unlocking-Gemini-2-0-Flash-Image-Generation-Potential">Unlocking Gemini 2.0 Flash’s Hidden Image Generation Potential</a></li><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/beyond-text-a-visual-revolution-in-ai-interaction">Beyond Text - A Visual Revolution in AI Interaction</a></li></ul></div><div role="group"><h3 class="yearGroupHeading_rMGB">2024</h3><ul class="sidebarItemList_Yudw clean-list"><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/how-to-leverage-ai-for-marketing-success-funblocks-aiflow-explained">How to Leverage AI for Marketing Success – FunBlocks AIFlow Explained</a></li><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/how-i-used-funblocks-ai-to-launch-successfully-on-product-hunt-a-real-world-marketing-case-study">How I Used FunBlocks AI to Launch Successfully on Product Hunt</a></li><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/i-developed-an-ai-infographic-generator-with-cursor-in-just-one-week">I Developed an AI Infographic Generator with Cursor in Just One Week</a></li><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/funblocks-aiflow-on-product-hunt-a-start-not-an-end">FunBlocks AIFlow on Product Hunt</a></li><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/beyond-chatgpt-how-llms-power-dynamic-ui-for-seamless-user-experience">How LLMs Power Dynamic UI for Seamless User Experience</a></li><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/what-if-asking-a-question-could-unlock-a-universe-of-knowledge">What If Asking a Question Could Unlock a Universe of Knowledge?</a></li><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/mindmap-llm-the-future-of-ai-interaction">Mindmap + LLM = The Future of AI Interaction?</a></li><li class="sidebarItem__DBe"><a class="sidebarItemLink_mo7H" href="/zh/blog/what-if-notion-ai-was-available-everywhere">What if Notion AI Was Available Everywhere?</a></li></ul></div></nav></aside><main class="col col--7"><article class="margin-bottom--xl"><header><h2 class="title_f1Hy"><a href="/zh/blog/when-the-internet-meets-ghibli-how-aiflow-turns-your-photos-into-an-animated-world">When the Internet Meets Ghibli - How AIFlow Turns Your Photos into an Animated World</a></h2><div class="container_mt6G margin-vert--md"><time datetime="2025-03-29T00:00:00.000Z">2025年3月29日</time> · <!-- -->阅读需 14 分钟</div><div class="margin-top--md margin-bottom--sm row"><div class="col col--12 authorCol_Hf19"><div class="avatar margin-bottom--sm"><div class="avatar__intro authorDetails_lV9A"><div class="avatar__name"><a href="/zh/blog/authors/wood"><span class="authorName_yefp">Wood Peng</span></a></div><small class="authorTitle_nd0D" title="SDE (Someone Do Everything) @ FunBlocks">SDE (Someone Do Everything) @ FunBlocks</small><div class="authorSocials_rSDt"><a href="https://x.com/woodpeng" target="_blank" rel="noopener noreferrer" class="authorSocialLink_owbf" title="X"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="none" viewBox="0 0 1200 1227" style="--dark:#000;--light:#fff" class="authorSocialLink_owbf xSvg_y3PF"><path d="M714.163 519.284 1160.89 0h-105.86L667.137 450.887 357.328 0H0l468.492 681.821L0 1226.37h105.866l409.625-476.152 327.181 476.152H1200L714.137 519.284h.026ZM569.165 687.828l-47.468-67.894-377.686-540.24h162.604l304.797 435.991 47.468 67.894 396.2 566.721H892.476L569.165 687.854v-.026Z"></path></svg></a><a href="https://www.linkedin.com/in/woodpeng/" target="_blank" rel="noopener noreferrer" class="authorSocialLink_owbf" title="LinkedIn"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" preserveAspectRatio="xMidYMid" viewBox="0 0 256 256" class="authorSocialLink_owbf"><path d="M218.123 218.127h-37.931v-59.403c0-14.165-.253-32.4-19.728-32.4-19.756 0-22.779 15.434-22.779 31.369v60.43h-37.93V95.967h36.413v16.694h.51a39.907 39.907 0 0 1 35.928-19.733c38.445 0 45.533 25.288 45.533 58.186l-.016 67.013ZM56.955 79.27c-12.157.002-22.014-9.852-22.016-22.009-.002-12.157 9.851-22.014 22.008-22.016 12.157-.003 22.014 9.851 22.016 22.008A22.013 22.013 0 0 1 56.955 79.27m18.966 138.858H37.95V95.967h37.97v122.16ZM237.033.018H18.89C8.58-.098.125 8.161-.001 18.471v219.053c.122 10.315 8.576 18.582 18.89 18.474h218.144c10.336.128 18.823-8.139 18.966-18.474V18.454c-.147-10.33-8.635-18.588-18.966-18.453" fill="#0A66C2"></path></svg></a></div></div></div></div></div></header><div class="markdown"><p>Social media has recently been swept away by a wonderful style: your friends, the accounts you follow, and even some unexpected brands, their avatars and shared photos all seem to have taken on a dreamlike color, full of the gentleness and whimsy of “Spirited Away” and “My Neighbor Totoro.” This trend, known as “Ghiblification,” is spreading across the internet at an astonishing rate.</p>
<p><strong>The Internet is Turning into Ghibli! (And You Can Too)</strong></p>
<p>Have you also noticed that recently, your social media feeds have been filled with a large number of charming, dreamlike images? They seem to have jumped directly out of the classic animated films of Studio Ghibli, whether it’s familiar scenes or users’ personal photos, all carrying that unique warmth and imagination that makes them instantly recognizable. The mysterious bathhouse in “Spirited Away,” the adorable Totoro in “My Neighbor Totoro”—these scenes that once only existed in our memories are now being presented to us in a brand new way 1.</p>
<p>This “Ghiblification” trend, as its name suggests, refers to transforming ordinary photos and even popular internet memes into the signature hand-drawn aesthetic of the renowned Japanese animation studio, Studio Ghibli, especially the works of its key figure, Hayao Miyazaki 1. Whether it’s turning a pet cat into the Baron Humbert von Gikkingen from “The Cat Returns” or transforming your own backyard into the tranquil countryside of “My Neighbor Totoro,” this magical transformation has sparked huge discussions and sharing frenzies on major social media platforms such as Twitter, Instagram, and Reddit 2.</p>
<p>So, how exactly is this “magic” that is sweeping the internet being achieved? The answer lies in a recent major leap in artificial intelligence technology.</p>
<p><strong>ChatGPT Unleashes Magic</strong></p>
<p>The driving force behind this visual style craze is the significant upgrade to ChatGPT’s image generation capabilities released by OpenAI on March 25, 2025 1. One of the most notable features of this update is the ability to skillfully transform existing images uploaded by users into completely new artistic styles 1. Users quickly grasped the fun of this feature and began experimenting with turning their own photos, and even various images from the internet, into “Ghibli-style” creations, sharing the results on social media and thus igniting this phenomenal trend 2. Social media platforms, including Instagram and X (formerly Twitter), have been flooded with these Ghibli-style images 1.</p>
<p>Style transfer refers to the ability of artificial intelligence to learn the unique visual characteristics of an art style (such as the soft colors, delicate lines, and imaginative compositions in Ghibli animation) and apply them to a completely different image. Imagine uploading a photo of your pet and then selecting “Ghibli style.” The AI can instantly generate a cute character image that looks like it came straight out of a Miyazaki animation 1. This simple yet powerful function has greatly stimulated users’ creativity and desire to share.</p>
<p>On social media, we can see all kinds of “Ghiblified” examples. One popular example involved a user who took a photo of their cat and asked ChatGPT to convert it to the Ghibli style, resulting in an anime image resembling a character from Miyazaki’s films 6. Others have turned their family photos into warm animated scenes, as if they were from a scene in “Ponyo”; some have transformed ordinary landscape photos into fantastical “Castle in the Sky”-like vistas 1. Even OpenAI CEO Sam Altman joined the trend, briefly changing his profile picture on X to a Studio Ghibli-style portrait of himself 1. This widespread adoption and sharing on platforms like Twitter quickly turned the trend into a viral phenomenon 1.</p>
<p>However, this carnival driven by free users did not last long. Due to overwhelming user demand and potential copyright issues, OpenAI subsequently temporarily restricted free users’ access to the image generation function 5. Nevertheless, this “Ghiblification” wave has fully demonstrated the huge potential of AI image generation technology and users’ love for this creative and fun interactive method.</p>
<p>The ease of use of this technology is a key factor in its rapid popularity. Users only need to simply upload an image, select a style, and they can get amazing results in a short time 2. This low barrier to entry allows both technology enthusiasts and ordinary users to easily participate in this creation, further accelerating its viral spread.</p>
<p>Of course, while AI art is booming, it is also accompanied by some discussions about copyright. OpenAI is also aware of this issue and has adopted a relatively cautious attitude, such as avoiding imitating the styles of living artists 1. This shows that while enjoying the convenience and fun brought by AI, we also need to pay attention to the ethical and legal issues it may bring.</p>
<p><strong>The Battle for AI Image Hegemony</strong></p>
<p>OpenAI’s decision to release such powerful image generation capabilities at this time may not be a coincidence. Just a short time before, on March 12, 2025, Google released its latest Gemini 2.0 Flash Exp model 10. This model also has powerful image generation and editing capabilities and emphasizes its multimodal features, capable of accepting various inputs including text, images, and even audio, and generating corresponding text, images, and audio output in the future 10. It is worth mentioning that Gemini 2.0 Flash Exp also has the ability of “conversational image editing,” allowing users to gradually refine and modify the generated images through natural language dialogue 12.</p>
<p>These two tech giants in a short time successively released groundbreaking image generation functions, undoubtedly demonstrating the fierce competition in this direction in the field of artificial intelligence. OpenAI’s rapid response is likely stimulated by Google’s continuous progress in image generation 11.</p>
<p>Artificial intelligence technology is developing at an astonishing speed, and major companies are vying to launch more powerful and convenient AI tools. Whether it’s ChatGPT’s style transfer or Gemini 2.0 Flash Exp’s multimodal capabilities, they all indicate that in the future, AI will achieve deeper integration and application in various media such as images, text, and audio 11.</p>
<p><strong><a href="https://www.funblocks.net/" target="_blank" rel="noopener noreferrer">FunBlocks AI</a>: Riding the Ghibli Wave</strong></p>
<p>In this wave of AI image generation, FunBlocks AI, with its keen insight and rapid action, integrated Google’s latest Gemini 2.0 Flash Exp model into its AIFlow platform at the first opportunity 14. This means that FunBlocks AI users can immediately experience image generation and modification functions similar to, or even more powerful than, ChatGPT 14.</p>
<p>After AIFlow integrated Gemini 2.0 Flash Exp, users can generate exquisite images by simply describing them in text, or intelligently modify existing images 14. More impressively, FunBlocks AI has innovatively introduced a “smart Prompt generation” step 14. When a user submits an image generation request, AIFlow first uses a large language model (LLM) to deeply analyze the user’s intent, then automatically generates a professional and detailed image generation Prompt, and finally calls the Gemini model to generate the image 14.</p>
<p>This two-stage approach greatly improves the quality of image generation and the user experience 9. Many users often feel confused about how to write effective Prompts when using AI image generation tools. FunBlocks AI’s innovative solution cleverly solves this problem. Users only need to describe their ideas in simple language, and the AI can automatically complete complex Prompt engineering and generate amazing images 9. This undoubtedly greatly enhances the overall user experience of AIFlow, allowing users to easily visualize any idea and scene, thus becoming more productive 9.</p>
<p>FunBlocks AI’s emphasis on user experience is reflected in its optimization of the Prompt generation process 9. By introducing LLMs for Prompt analysis and optimization, FunBlocks AI lowers the barrier to entry for users, making it easy for even those without professional knowledge to create high-quality AI images 9. This user-centric design philosophy is an important factor for FunBlocks AI to stand out in the fierce market competition 9.</p>
<p>In addition, FunBlocks AI’s choice to develop based on powerful foundation models like Gemini 2.0 Flash Exp also reflects a wise strategy 14. By leveraging the powerful capabilities of existing advanced models, FunBlocks AI can focus more on optimizing the user interface and developing unique features, thereby providing users with better products and services 14.</p>
<p><strong>Your Creative Toolbox: <a href="https://www.funblocks.net/aitools" target="_blank" rel="noopener noreferrer">FunBlocks AI Tools</a></strong></p>
<p>In addition to the powerful image generation and modification functions in AIFlow, FunBlocks AI Tools has also added two practical gadgets: FunBlocks AI Avatar Studio and FunBlocks AI Watermark Eraser .</p>
<p><strong>A. <a href="https://www.funblocks.net/aitools/avatar" target="_blank" rel="noopener noreferrer">FunBlocks AI Avatar Studio</a>: Your Ghibli Transformation Station</strong></p>
<p>FunBlocks AI Avatar Studio is a powerful artificial intelligence avatar generation tool. Users only need to upload a photo or paste an image link to instantly transform the photo into a digital avatar in various artistic styles 18. Avatar Studio offers an extremely rich selection of artistic styles, covering everything from surrealism to cartoon animation, from classic oil paintings to trendy graffiti 19.</p>
<p>The most exciting thing is that FunBlocks AI Avatar Studio also keeps up with the trend and has built-in the popular Ghibli style! Users only need to upload their photos, select the Ghibli style, and they can instantly generate an exclusive avatar full of Ghibli animation charm 19. Whether you want to have the mysterious temperament of the protagonist in “Howl’s Moving Castle” or the cute image of Kiki in “Kiki’s Delivery Service,” Avatar Studio can easily help you achieve it 19. Moreover, the generation process is extremely simple, truly achieving one-click generation without any design skills 19.</p>
<p><img decoding="async" loading="lazy" src="https://miro.medium.com/v2/resize:fit:1400/format:webp/1*SKBIeQIb8gefZhD2k2XHhQ.png" alt="" class="img_ev3q"></p>
<p>Ghibli-styled Lord of the Rings stills generated by FunBlocks AI Avatar Generator</p>
<p>In addition to the Ghibli style, Avatar Studio also supports various other popular anime and cartoon styles, such as Japanese anime, American comics, cyberpunk, steampunk, and more 19. No matter which style you like, you can find your unique avatar in Avatar Studio. The generated avatars are also of very high quality and can be used in various scenarios such as social media and game platforms 19.</p>
<p>FunBlocks AI keenly captures users’ demand for personalized avatars and their pursuit of popular artistic styles. Integrating the Ghibli style into Avatar Studio, undoubtedly inspired by the recent viral trend on platforms like Twitter, is an important move for it to stand out among many AI avatar generation tools 1.</p>
<p><strong>B. FunBlocks AI Watermark Eraser: Easily Remove Watermarks</strong></p>
<p>FunBlocks AI Watermark Eraser is an artificial intelligence-based watermark removal tool . It can help users quickly and efficiently remove various types of watermarks from images, including semi-transparent watermarks, complex textured watermarks, and text watermarks .</p>
<p>For users who need to use online images but are troubled by watermarks, this tool is undoubtedly a godsend . Whether it’s professional designers who need clean materials or individual users who want to share watermark-free photos, Watermark Eraser can provide a convenient solution . Users only need to upload an image or paste an image link, click process, and the AI can intelligently identify and remove the watermark while trying its best to preserve the original details and colors of the image . The operation process is very simple and does not require professional image editing skills .</p>
<p>FunBlocks AI Watermark Eraser’s launch reflects its commitment to providing users with practical and convenient AI tools . This tool’s addition further improves FunBlocks AI Tools’ functionality and meets more of users’ needs in image processing .</p>
<p><strong>Become Your Own Miyazaki: Immediately Experience FunBlocks AI Avatar Studio!</strong></p>
<p>Still envious of others having avatars full of Ghibli style? Now, you can easily have one too! Hurry up and visit the FunBlocks AI official website (<a href="https://www.funblocks.net/aitools/avatar" target="_blank" rel="noopener noreferrer">https://www.funblocks.net/aitools/avatar</a>) and personally experience the powerful features of FunBlocks AI Avatar Studio! With just a click, you can turn your photos into Ghibli-style avatars full of dreamlike colors.</p>
<p>Don’t hesitate, let FunBlocks AI Avatar Studio help you realize your animation dreams and show your unique charm on social media! Remember to share your creative works and let more people feel the artistic fun brought by AI!</p>
<p><strong>Conclusion</strong></p>
<p>From the “Ghiblification” wave ignited by ChatGPT, with countless examples shared on Twitter and other platforms, to FunBlocks AI quickly responding and launching related tools, we have seen the huge potential and vitality of artificial intelligence technology in the field of images 1. FunBlocks AI, with its deep understanding of user needs and rapid application of cutting-edge technologies like Gemini 2.0 Flash Exp, brings users a more convenient, efficient, and interesting AI experience 14. Whether you want to have a unique Ghibli-style avatar, inspired by the social media trend, or need to remove image watermarks, FunBlocks AI can become your powerful assistant . This creative wave driven by AI is still continuing, and FunBlocks AI is standing at the forefront, leading us to explore more exciting possibilities .</p>
<p><strong>Table 1: Comparison of Image Generation Capabilities of ChatGPT and Gemini 2.0 Flash Exp (as of March 2025)</strong></p>
<ul>
<li>Feature: Style Transfer<!-- -->
<ul>
<li>ChatGPT (as of March 25, 2025): Yes (sparked the Ghibli trend)</li>
<li>Gemini 2.0 Flash Exp (as of March 12, 2025): Yes</li>
</ul>
</li>
<li>Feature: Multimodal Input/Output<!-- -->
<ul>
<li>ChatGPT (as of March 25, 2025): Text to image, image to image</li>
<li>Gemini 2.0 Flash Exp (as of March 12, 2025): Text, image (experimental), and audio (coming soon)</li>
</ul>
</li>
<li>Feature: Conversational Image Editing<!-- -->
<ul>
<li>ChatGPT (as of March 25, 2025): Not explicitly mentioned</li>
<li>Gemini 2.0 Flash Exp (as of March 12, 2025): Yes</li>
</ul>
</li>
<li>Feature: Text Rendering<!-- -->
<ul>
<li>ChatGPT (as of March 25, 2025): Improved in GPT-4o</li>
<li>Gemini 2.0 Flash Exp (as of March 12, 2025): Stronger than competitors</li>
</ul>
</li>
</ul>
<p><strong>Table 2: FunBlocks AI Avatar Studio – Style Categories and Examples</strong></p>
<ul>
<li>Style Category: Hyper-Realistic &amp; Cinematic<!-- -->
<ul>
<li>Example Styles: Studio Photography, CGI Movie-Grade Portraits, Semi-Realistic Art</li>
</ul>
</li>
<li>Style Category: Anime &amp; Cartoon<!-- -->
<ul>
<li>Example Styles: Japanese Anime, Comic Book Style, Ghibli Style, American Superhero, Chibi &amp; Cute Style, Cyberpunk, Steampunk</li>
</ul>
</li>
<li>Style Category: Fantasy &amp; Sci-Fi<!-- -->
<ul>
<li>Example Styles: Epic Fantasy, Fairy &amp; Elven Style, Gothic &amp; Vampire, Alien &amp; Cosmic, Mecha Warrior</li>
</ul>
</li>
<li>Style Category: Retro &amp; Artistic<!-- -->
<ul>
<li>Example Styles: Oil Painting, Chinese Ink Painting, Old-School Cartoon, Pixel Art, Pop Art</li>
</ul>
</li>
<li>Style Category: Trendy &amp; Unique<!-- -->
<ul>
<li>Example Styles: Minimalist Line Art, Neon Glow, Black &amp; White Silhouette, Street Graffiti, Caricature &amp; Exaggerated Styles</li>
</ul>
</li>
<li>Style Category: Special Transformations &amp; Costumes<!-- -->
<ul>
<li>Example Styles: Anime Character Cosplay, Fantasy RPG Look, Furry &amp; Animal-Inspired, Sci-Fi Hacker &amp; Cyber Assassin, Astronaut &amp; Space Explorer</li>
</ul>
</li>
</ul>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="references">References<a href="#references" class="hash-link" aria-label="References的直接链接" title="References的直接链接">​</a></h4>
<ol>
<li>Legal questions arise as fans flood internet with Ghibli-style AI memes – KING 5,  <a href="https://www.king5.com/article/news/nation-world/chatgpt-ghibli-style-ai-copyright-concerns/507-cf747827-4623-49b0-bdea-b0c76e39be54" target="_blank" rel="noopener noreferrer">https://www.king5.com/article/news/nation-world/chatgpt-ghibli-style-ai-copyright-concerns/507-cf747827-4623-49b0-bdea-b0c76e39be54</a></li>
<li>How To Create Studio Ghibli-Style AI Images On ChatGPT For Free – NDTV,  <a href="https://www.ndtv.com/world-news/how-to-create-studio-ghibli-style-ai-images-on-chatgpt-for-free-8029848" target="_blank" rel="noopener noreferrer">https://www.ndtv.com/world-news/how-to-create-studio-ghibli-style-ai-images-on-chatgpt-for-free-8029848</a></li>
<li>Studio Ghibli-style images: Here’s how to create AI-generated pictures on ChatGPT for free using OpenAI’s GPT-4o – The Economic Times,  <a href="https://m.economictimes.com/news/international/us/entertainment/studio-ghibli-style-images-heres-how-to-create-ai-generated-pictures-on-chatgpt-for-free-using-openais-gpt-4o/articleshow/119600454.cms" target="_blank" rel="noopener noreferrer">https://m.economictimes.com/news/international/us/entertainment/studio-ghibli-style-images-heres-how-to-create-ai-generated-pictures-on-chatgpt-for-free-using-openais-gpt-4o/articleshow/119600454.cms</a></li>
<li>ChatGPT’s new image generator is wild, 5 ways to create fascinating images,  <a href="https://indianexpress.com/article/technology/artificial-intelligence/chatgpt-image-generator-5-tips-for-better-images-9911431/" target="_blank" rel="noopener noreferrer">https://indianexpress.com/article/technology/artificial-intelligence/chatgpt-image-generator-5-tips-for-better-images-9911431/</a></li>
<li>OpenAI pauses free GPT-4o image generation after viral Studio Ghibli trend – Tech Edition,  <a href="https://www.techedt.com/openai-pauses-free-gpt-4o-image-generation-after-viral-studio-ghibli-trend" target="_blank" rel="noopener noreferrer">https://www.techedt.com/openai-pauses-free-gpt-4o-image-generation-after-viral-studio-ghibli-trend</a></li>
<li>How to generate Ghibli-style AI portraits using Grok 3 — no ChatGPT subscription needed,  <a href="https://www.livemint.com/technology/tech-news/how-to-generate-ghibli-style-ai-images-using-xai-grok-3-no-chatgpt-subscription-needed-elon-musk-11743214190572.html" target="_blank" rel="noopener noreferrer">https://www.livemint.com/technology/tech-news/how-to-generate-ghibli-style-ai-images-using-xai-grok-3-no-chatgpt-subscription-needed-elon-musk-11743214190572.html</a></li>
<li>Gemini Flash – Google DeepMind,  <a href="https://deepmind.google/technologies/gemini/flash/" target="_blank" rel="noopener noreferrer">https://deepmind.google/technologies/gemini/flash/</a></li>
<li>OpenAI Cools Down: ChatGPT Image Generation Temporarily Limited Due to GPU Overload,  <a href="https://opentools.ai/news/openai-cools-down-chatgpt-image-generation-temporarily-limited-due-to-gpu-overload" target="_blank" rel="noopener noreferrer">https://opentools.ai/news/openai-cools-down-chatgpt-image-generation-temporarily-limited-due-to-gpu-overload</a></li>
<li>AI-generated Ghibli-style images overwhelm OpenAI servers | Digital Watch Observatory,  <a href="https://dig.watch/updates/ai-generated-ghibli-style-images-overwhelm-openai-servers" target="_blank" rel="noopener noreferrer">https://dig.watch/updates/ai-generated-ghibli-style-images-overwhelm-openai-servers</a></li>
<li>Release notes | Gemini API | Google AI for Developers,  <a href="https://ai.google.dev/gemini-api/docs/changelog" target="_blank" rel="noopener noreferrer">https://ai.google.dev/gemini-api/docs/changelog</a></li>
<li>Vertex AI release notes | Generative AI – Google Cloud,  <a href="https://cloud.google.com/vertex-ai/generative-ai/docs/release-notes" target="_blank" rel="noopener noreferrer">https://cloud.google.com/vertex-ai/generative-ai/docs/release-notes</a></li>
<li>You can now test Gemini 2.0 Flash’s native image output – 9to5Google,  <a href="https://9to5google.com/2025/03/12/gemini-2-0-flash-native-image-output/" target="_blank" rel="noopener noreferrer">https://9to5google.com/2025/03/12/gemini-2-0-flash-native-image-output/</a></li>
<li>Gemini 2.0 model updates: 2.0 Flash, Flash-Lite, Pro Experimental – The Keyword,  <a href="https://blog.google/technology/google-deepmind/gemini-model-updates-february-2025/" target="_blank" rel="noopener noreferrer">https://blog.google/technology/google-deepmind/gemini-model-updates-february-2025/</a></li>
<li>FunBlocks AIFlow New Experience powered by Google Gemini-2.0-flash – YouTube,  <a href="https://www.youtube.com/watch?v=YoM-HMeRMKc" target="_blank" rel="noopener noreferrer">https://www.youtube.com/watch?v=YoM-HMeRMKc</a></li>
<li>Gemini models | Gemini API | Google AI for Developers,  <a href="https://ai.google.dev/gemini-api/docs/models" target="_blank" rel="noopener noreferrer">https://ai.google.dev/gemini-api/docs/models</a></li>
<li>Experiment with Gemini 2.0 Flash native image generation – Google Developers Blog,  <a href="https://developers.googleblog.com/en/experiment-with-gemini-20-flash-native-image-generation/" target="_blank" rel="noopener noreferrer">https://developers.googleblog.com/en/experiment-with-gemini-20-flash-native-image-generation/</a></li>
<li>Unlocking Gemini 2.0 Flash’s Hidden Image Generation Potential with FunBlocks AIFlow | by Woodpeng | Mar, 2025 | Medium,  <a href="https://medium.com/@woodpeng/ai-image-generation-with-funblocks-aiflow-powered-by-gemini-2-0-flash-expunlocking-45a713365f7a" target="_blank" rel="noopener noreferrer">https://medium.com/@woodpeng/ai-image-generation-with-funblocks-aiflow-powered-by-gemini-2-0-flash-expunlocking-45a713365f7a</a></li>
<li>FunBlocks AI – Your AI-powered workspace for enhanced creativity …,  <a href="https://www.funblocks.net/" target="_blank" rel="noopener noreferrer">https://www.funblocks.net</a></li>
<li>AI Avatar Studio – Instantly Create Stunning AI-Generated Avatars …,  <a href="https://www.funblocks.net/aitools/avatar" target="_blank" rel="noopener noreferrer">https://www.funblocks.net/aitools/avatar</a></li>
</ol></div><footer class="row docusaurus-mt-lg"><div class="col"><b>标签：</b><ul class="tags_jXut padding--none margin-left--sm"><li class="tag_QGVx"><a title="Ideas" class="tag_zVej tagRegular_sFm0" href="/zh/blog/tags/hello">Idea</a></li><li class="tag_QGVx"><a title="Product feature" class="tag_zVej tagRegular_sFm0" href="/zh/blog/tags/feature">Feature</a></li><li class="tag_QGVx"><a title="FunBlocks AIFlow" class="tag_zVej tagRegular_sFm0" href="/zh/blog/tags/aiflow">AIFlow</a></li></ul></div></footer></article><nav class="pagination-nav" aria-label="博文列表分页导航"><a class="pagination-nav__link pagination-nav__link--prev" href="/zh/blog/page/2"><div class="pagination-nav__label">较新的博文</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/zh/blog/page/4"><div class="pagination-nav__label">较旧的博文</div></a></nav></main></div></div></div></div>
</body>
</html>